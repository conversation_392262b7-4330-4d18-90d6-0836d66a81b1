{"cells": [{"cell_type": "code", "execution_count": 12, "id": "577e9195", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NpzFile '/media/main/ypf/eeg/MSA-CNN/data_handler/data/SC4002E0.npz' with keys: x, y, fs, ch_label, header_raw...\n", "['x', 'y', 'fs', 'ch_label', 'header_raw', 'header_annotation']\n"]}], "source": ["import numpy as np\n", "import os\n", "\n", "file_path = \"/media/main/ypf/eeg/MSA-CNN/data_handler/data/SC4002E0.npz\"\n", "\n", "data = np.load(file_path, allow_pickle=True)\n", "print(data)\n", "print(data.files)"]}, {"cell_type": "code", "execution_count": 23, "id": "54ce58b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Key: x, Shape: (1127, 3000, 1)\n", "Key: y, Shape: (1127,)\n", "Key: fs, Shape: ()\n", "Key: ch_label, Shape: ()\n", "Key: header_raw, Shape: ()\n", "Key: header_annotation, Shape: ()\n"]}], "source": ["for key, array in data.items():\n", "    print(f\"Key: {key}, Shape: {array.shape}\")"]}, {"cell_type": "code", "execution_count": 28, "id": "1fed5c88", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(100.)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"fs\"]"]}], "metadata": {"kernelspec": {"display_name": "msa", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}