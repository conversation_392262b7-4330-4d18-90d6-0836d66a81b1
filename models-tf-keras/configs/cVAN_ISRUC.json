{"channels": 10, "n_length": 100, "channels_to_use": ["F3_A2", "C3_A2", "F4_A1", "C4_A1", "O1_A2", "O2_A1", "ROC_A1", "LOC_A2", "X1", "X2"], "exclude_subjects_data": [], "exclude_subjects_label": [], "label_path": "../data/ISRUC/RawData/", "path_data": "../data/ISRUC/ExtractedChannels/", "path_prepared_data": "../data/ISRUC/", "path_preprocessed_data": "../data/ISRUC/ISRUC_preprocessing_method_label_type.npy", "path_output": "results/cVAN_ISRUC/models/", "path_feature": "results/cVAN_ISRUC/features/", "path_evaluation": "results/cVAN_ISRUC/evaluation/", "num_runs": 10}