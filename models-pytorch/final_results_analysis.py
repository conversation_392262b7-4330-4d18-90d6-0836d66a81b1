#!/usr/bin/env python3
"""
综合分析所有模型的训练结果
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime


def analyze_all_results():
    """分析所有训练结果"""

    print("=" * 80)
    print("MSA-CNN 深度学习模型训练结果综合分析")
    print("=" * 80)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 查找所有结果文件
    result_files = []
    result_files.extend(glob.glob("results/overview/overview_results_*.csv"))
    result_files.extend(glob.glob("results/epoch_details/main_results_*.csv"))

    if not result_files:
        print("未找到训练结果文件！")
        return

    print(f"找到 {len(result_files)} 个结果文件:")
    for f in result_files:
        print(f"  - {f}")
    print()

    # 合并所有结果
    all_results = []
    for file in result_files:
        try:
            df = pd.read_csv(file)
            all_results.append(df)
            print(f"成功读取 {file}: {len(df)} 条记录")
        except Exception as e:
            print(f"读取 {file} 失败: {e}")

    if not all_results:
        print("没有成功读取任何结果文件！")
        return

    # 合并数据
    combined_df = pd.concat(all_results, ignore_index=True)
    print(f"\n总共合并了 {len(combined_df)} 条训练记录")

    # 过滤有效结果（test_accuracy > 0）
    valid_results = combined_df[
        (combined_df["test_accuracy"] > 0) & (combined_df["run_complete"] == True)
    ].copy()

    print(f"有效完成的训练记录: {len(valid_results)} 条")
    print()

    if len(valid_results) == 0:
        print("没有找到有效的训练结果！")
        return

    # 按模型和数据集分组分析
    print("=" * 60)
    print("模型性能汇总 (按测试准确率排序)")
    print("=" * 60)

    # 创建汇总表
    summary_data = []

    for _, row in valid_results.iterrows():
        model_name = row["model"]
        dataset = row["data"]
        channel_sel = row.get("channel_selection", "N/A")
        test_acc = row["test_accuracy"] * 100  # 转换为百分比
        train_acc = row["train_accuracy"] * 100 if row["train_accuracy"] > 0 else 0
        duration = row.get("duration_seconds", 0) / 60  # 转换为分钟
        params = row.get("trainable_params", 0)

        summary_data.append(
            {
                "Model": model_name,
                "Dataset": dataset,
                "Channel": channel_sel,
                "Test_Acc(%)": f"{test_acc:.2f}",
                "Train_Acc(%)": f"{train_acc:.2f}",
                "Duration(min)": f"{duration:.1f}",
                "Parameters": f"{params:,}" if params > 0 else "N/A",
            }
        )

    # 转换为DataFrame并排序
    summary_df = pd.DataFrame(summary_data)
    summary_df["Test_Acc_Numeric"] = summary_df["Test_Acc(%)"].astype(float)
    summary_df = summary_df.sort_values("Test_Acc_Numeric", ascending=False)
    summary_df = summary_df.drop("Test_Acc_Numeric", axis=1)

    # 显示结果表格
    print(summary_df.to_string(index=False))
    print()

    # 按模型类型统计最佳性能
    print("=" * 60)
    print("各模型最佳性能统计")
    print("=" * 60)

    model_best = {}
    for _, row in valid_results.iterrows():
        model = row["model"]
        test_acc = row["test_accuracy"] * 100

        if model not in model_best or test_acc > model_best[model]["accuracy"]:
            model_best[model] = {
                "accuracy": test_acc,
                "dataset": row["data"],
                "channel": row.get("channel_selection", "N/A"),
                "params": row.get("trainable_params", 0),
            }

    for model, info in sorted(
        model_best.items(), key=lambda x: x[1]["accuracy"], reverse=True
    ):
        print(
            f"{model:15s}: {info['accuracy']:6.2f}% (数据集: {info['dataset']}, 通道: {info['channel']}, 参数: {info['params']:,})"
        )

    print()

    # 按数据集统计
    print("=" * 60)
    print("各数据集性能统计")
    print("=" * 60)

    for dataset in ["sleep_edf_20", "sleep_edf_78"]:
        dataset_results = valid_results[valid_results["data"] == dataset]
        if len(dataset_results) > 0:
            best_acc = dataset_results["test_accuracy"].max() * 100
            avg_acc = dataset_results["test_accuracy"].mean() * 100
            best_model = dataset_results.loc[
                dataset_results["test_accuracy"].idxmax(), "model"
            ]
            print(
                f"{dataset:15s}: 最佳 {best_acc:.2f}% ({best_model}), 平均 {avg_acc:.2f}%"
            )

    print()

    # 保存详细结果
    output_file = (
        f"final_training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    )
    summary_df.to_csv(output_file, index=False)
    print(f"详细结果已保存到: {output_file}")

    print()
    print("=" * 80)
    print("分析完成！")
    print("=" * 80)


if __name__ == "__main__":
    analyze_all_results()
