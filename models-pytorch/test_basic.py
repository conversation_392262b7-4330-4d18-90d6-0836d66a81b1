#!/usr/bin/env python3
"""
Basic test to verify core functionality without data_handler dependencies.
"""

import sys
import os


def test_basic_imports():
    """Test basic imports that should work."""
    try:
        # Test PyTorch
        import torch

        print(f"✓ PyTorch {torch.__version__} imported successfully")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")

        # Test other core packages
        import numpy as np

        print(f"✓ NumPy {np.__version__} imported successfully")

        import pandas as pd

        print(f"✓ Pandas {pd.__version__} imported successfully")

        # Test local modules
        from utils.utils import unique

        print("✓ utils.utils imported successfully")

        from config_manager.config_loader import config_generator

        print("✓ config_manager imported successfully")

        from models.EEGNet import EEGNet

        print("✓ EEGNet model imported successfully")

        return True

    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_model_creation():
    """Test creating a simple model."""
    try:
        import torch
        from models.EEGNet import EEGNet

        # Create a simple config for EEGNet
        config = {"channels": 64}

        # Create a simple EEGNet model
        model = EEGNet(config=config, dropoutRate=0.5, F1=8, D=2, F2=16)

        print(f"✓ EEGNet model created successfully")
        print(f"✓ Model parameters: {sum(p.numel() for p in model.parameters())}")

        # Test forward pass with correct dimensions (batch, 1, channels, time_samples)
        x = torch.randn(1, 1, 64, 3000)  # EEGNet expects 4D input
        output = model(x)
        print(f"✓ Forward pass successful, output shape: {output.shape}")

        return True

    except Exception as e:
        print(f"✗ Model test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run basic tests."""
    print("MSA-CNN Basic Functionality Test")
    print("=" * 50)

    success = True

    print("\n1. Testing basic imports...")
    if not test_basic_imports():
        success = False

    print("\n2. Testing model creation...")
    if not test_model_creation():
        success = False

    print("\n" + "=" * 50)
    if success:
        print("🎉 All basic tests passed!")
        print("\n注意: data_handler模块可能需要额外配置，但核心功能正常工作。")
        print("你可以尝试运行特定的模型配置来测试完整功能。")
    else:
        print("❌ Some tests failed.")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
